# 前端适配指南 - 数据库会话存储方案

## 概述

由于用户会话管理改为数据库存储，前端需要在请求中包含 `deviceId` 参数，并调整SSE连接方式。

## 1. 设备ID管理

### 生成和存储设备ID

```javascript
// 生成设备ID的函数
function generateDeviceId() {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(2);
  return `device_${timestamp}_${random}`;
}

// 获取或生成设备ID
function getDeviceId() {
  let deviceId = localStorage.getItem('deviceId');
  if (!deviceId) {
    deviceId = generateDeviceId();
    localStorage.setItem('deviceId', deviceId);
  }
  return deviceId;
}
```

## 2. 登录请求调整

### 原有方式
```javascript
const response = await fetch(`${API_URL}/auth/login`, {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    userId: userId,
    phone: phone,
    forceLogin: forceLogin,
    completeUserInfo: userInfo
  })
});
```

### 新方式（需要添加deviceId）
```javascript
const response = await fetch(`${API_URL}/auth/login`, {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    userId: userId,
    phone: phone,
    deviceId: getDeviceId(), // 添加设备ID
    forceLogin: forceLogin,
    completeUserInfo: userInfo
  })
});

const data = await response.json();
if (data.success) {
  sessionId = data.sessionId;
  localStorage.setItem('sessionId', sessionId);
  localStorage.setItem('deviceId', data.deviceId); // 保存服务器返回的设备ID
  // ... 其他登录成功处理
}
```

## 3. SSE连接调整

### 原有方式
```javascript
const sseUrl = `${API_URL}/auth/sse?sessionId=${sessionId}&clientId=${clientId}`;
```

### 新方式（需要添加deviceId）
```javascript
function connectSSE() {
  const sessionId = localStorage.getItem('sessionId');
  const deviceId = localStorage.getItem('deviceId');
  const clientId = localStorage.getItem('clientId') || generateClientId();
  
  if (!sessionId || !deviceId) {
    console.log('缺少会话信息，无法建立SSE连接');
    return;
  }

  // 关闭旧连接
  if (sseConnection) {
    sseConnection.close();
    sseConnection = null;
  }

  // 建立新连接，包含deviceId
  const sseUrl = `${API_URL}/auth/sse?sessionId=${sessionId}&deviceId=${deviceId}&clientId=${clientId}`;
  
  sseConnection = new EventSource(sseUrl);
  
  sseConnection.onopen = () => {
    console.log('SSE连接已建立');
  };

  sseConnection.onmessage = (event) => {
    try {
      const data = JSON.parse(event.data);
      handleSSEMessage(data);
    } catch (error) {
      console.error('处理SSE消息时出错:', error);
    }
  };

  sseConnection.onerror = (error) => {
    console.error('SSE连接错误:', error);
    
    if (sseConnection.readyState === EventSource.CLOSED) {
      console.log('SSE连接断开，3秒后重连...');
      setTimeout(() => connectSSE(), 3000);
    }
  };
}
```

## 4. 会话检查调整

### 原有方式
```javascript
const response = await fetch(`${API_URL}/auth/check-session`, {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ sessionId })
});
```

### 新方式（可选添加deviceId）
```javascript
async function checkSession() {
  const sessionId = localStorage.getItem('sessionId');
  const deviceId = localStorage.getItem('deviceId');
  
  if (!sessionId) {
    return { valid: false, message: '缺少会话ID' };
  }

  try {
    const response = await fetch(`${API_URL}/auth/check-session`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ 
        sessionId,
        deviceId // 可选，用于设备验证
      })
    });

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('检查会话失败:', error);
    return { valid: false, message: '网络错误' };
  }
}
```

## 5. 其他API请求调整

对于其他需要会话验证的API请求，建议也包含deviceId：

```javascript
async function apiRequest(url, options = {}) {
  const sessionId = localStorage.getItem('sessionId');
  const deviceId = localStorage.getItem('deviceId');

  if (!sessionId) {
    throw new Error('缺少会话信息，请重新登录');
  }

  const requestOptions = {
    ...options,
    headers: {
      'Content-Type': 'application/json',
      ...options.headers
    }
  };

  if (options.method === 'POST' && options.body) {
    const bodyData = JSON.parse(options.body);
    bodyData.sessionId = sessionId;
    bodyData.deviceId = deviceId; // 添加设备ID
    requestOptions.body = JSON.stringify(bodyData);
  }

  return fetch(url, requestOptions);
}
```

## 6. 错误处理增强

### 处理设备不匹配错误
```javascript
sseConnection.onmessage = (event) => {
  try {
    const data = JSON.parse(event.data);
    
    if (data.type === 'error') {
      if (data.message.includes('设备不匹配')) {
        // 设备不匹配，清理本地存储并重新登录
        localStorage.removeItem('sessionId');
        localStorage.removeItem('deviceId');
        showMessage('设备验证失败，请重新登录', 'warning');
        showLoginForm();
        return;
      }
    }
    
    handleSSEMessage(data);
  } catch (error) {
    console.error('处理SSE消息时出错:', error);
  }
};
```

### 处理强制登录响应
```javascript
const data = await response.json();

if (!data.success && data.requireForceLogin) {
  // 显示强制登录确认对话框
  const forceLogin = confirm(`${data.message}\n\n是否强制登录？`);
  
  if (forceLogin) {
    // 重新发送登录请求，设置forceLogin为true
    const forceResponse = await fetch(`${API_URL}/auth/login`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        userId: userId,
        phone: phone,
        deviceId: getDeviceId(),
        forceLogin: true,
        completeUserInfo: userInfo
      })
    });
    
    const forceData = await forceResponse.json();
    if (forceData.success) {
      // 强制登录成功
      handleLoginSuccess(forceData);
    }
  }
}
```

## 7. 完整的登录流程示例

```javascript
async function login(phone, password, forceLogin = false) {
  try {
    const deviceId = getDeviceId();
    
    const response = await fetch(`${API_URL}/auth/login`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        userId: userId,
        phone: phone,
        deviceId: deviceId,
        forceLogin: forceLogin,
        completeUserInfo: userInfo
      })
    });

    const data = await response.json();
    
    if (data.success) {
      // 登录成功
      localStorage.setItem('sessionId', data.sessionId);
      localStorage.setItem('deviceId', data.deviceId);
      localStorage.setItem('userId', data.user?.id);
      localStorage.setItem('phone', phone);
      
      // 建立SSE连接
      connectSSE();
      
      // 显示登录成功界面
      showLoggedInUser(phone, data.user);
      
      return { success: true };
    } else if (data.requireForceLogin) {
      // 需要强制登录
      return { 
        success: false, 
        requireForceLogin: true, 
        message: data.message,
        currentDevice: data.currentDevice 
      };
    } else {
      // 其他错误
      return { success: false, message: data.message };
    }
  } catch (error) {
    console.error('登录失败:', error);
    return { success: false, message: '网络错误' };
  }
}
```

## 总结

主要变更点：
1. **添加设备ID管理**：生成、存储和传递设备ID
2. **登录请求**：包含deviceId参数
3. **SSE连接**：URL中包含deviceId参数
4. **会话检查**：可选包含deviceId进行设备验证
5. **错误处理**：处理设备不匹配等新的错误类型

这些调整确保了单设备登录控制的正常工作，并提供了更好的用户体验。
