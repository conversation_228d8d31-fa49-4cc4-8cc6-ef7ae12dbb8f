import { DurableObject } from 'cloudflare:workers';
import type { Env, SSEConnection, UserSession, SessionManager } from './types';

// 用户认证Durable Object
export class UserAuthDO extends DurableObject<Env> {
  private sessions: SessionManager; // 保留给管理员使用
  private sseConnections: Map<string, SSEConnection>; // 用户SSE连接管理
  private adminSSEConnections: Map<string, ReadableStreamDefaultController<Uint8Array>>; // 管理员SSE连接
  private initialized: boolean = false;

  constructor(ctx: DurableObjectState, env: Env) {
    super(ctx, env);

    this.sessions = {} as SessionManager;
    this.sessions.byPhone = new Map<string, number>();
    this.sessions.bySessionId = new Map<string, number>();
    this.sseConnections = new Map<string, SSEConnection>();
    this.adminSSEConnections = new Map<string, ReadableStreamDefaultController<Uint8Array>>();

    // 确保会话数据在任何操作前加载一次
    this.loadSessions();

    console.log('UserAuthDO 已初始化');
  }



  // 发送SSE消息
  private sendSSEMessage(controller: ReadableStreamDefaultController<Uint8Array>, data: any): void {
    try {
      const message = `data: ${JSON.stringify(data)}\n\n`;
      controller.enqueue(new TextEncoder().encode(message));
    } catch (error) {
      console.error('发送SSE消息失败:', error);
    }
  }

  // 验证管理员权限
  private async validateAdminAuth(request: Request): Promise<{ valid: boolean; message?: string }> {
    try {
      // 从请求头获取管理员认证信息
      const authHeader = request.headers.get('Authorization');
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return { valid: false, message: "缺少管理员认证信息" };
      }

      const token = authHeader.substring(7); // 移除 "Bearer " 前缀

      // 验证管理员token（这里可以是简单的密码验证或JWT验证）
      const storedPassword = await this.env.ADMIN_KV.get('admin_password');
      const storedUsername = await this.env.ADMIN_KV.get('admin_username');

      if (!storedPassword || !storedUsername) {
        return { valid: false, message: "管理员账号未初始化" };
      }

      // 简单验证：token应该是 username:password 的base64编码
      try {
        const decoded = atob(token);
        const [username, password] = decoded.split(':');

        if (username !== storedUsername || password !== storedPassword) {
          return { valid: false, message: "管理员认证失败" };
        }

        return { valid: true };
      } catch (error) {
        return { valid: false, message: "无效的认证格式" };
      }
    } catch (error) {
      return { valid: false, message: `管理员权限验证失败: ${error}` };
    }
  }

  // ==================== 用户会话数据库管理方法 ====================

  // 检查数据库中的现有会话
  private async checkExistingSession(userId: number, phone: string): Promise<{
    hasActiveSession: boolean;
    sessionId?: string;
    deviceId?: string;
    lastActiveTime?: number;
  }> {
    try {
      const result = await this.env.DB.prepare(`
        SELECT session_id, device_id, last_active_time, is_online
        FROM users
        WHERE id = ? AND phone = ? AND session_id IS NOT NULL
      `).bind(userId, phone).first();

      if (!result || !result.session_id) {
        return { hasActiveSession: false };
      }

      if (!result.is_online) {
        // 用户已离线，清理数据库记录
        await this.clearUserSessionFromDB(userId);
        return { hasActiveSession: false };
      }

      return {
        hasActiveSession: true,
        sessionId: String(result.session_id),
        deviceId: String(result.device_id),
        lastActiveTime: Number(result.last_active_time) || 0
      };

    } catch (error) {
      console.error('检查现有会话失败:', error);
      return { hasActiveSession: false };
    }
  }

  // 创建新的用户会话
  private async createUserSession(userId: number, phone: string, sessionId: string, deviceId: string): Promise<void> {
    try {
      const now = Date.now();

      await this.env.DB.prepare(`
        UPDATE users
        SET session_id = ?,
            session_created_at = ?,
            last_active_time = ?,
            device_id = ?,
            is_online = TRUE
        WHERE id = ? AND phone = ?
      `).bind(sessionId, now, now, deviceId, userId, phone).run();

      console.log(`为用户 ${userId} 创建新会话: ${sessionId}`);
    } catch (error) {
      console.error('创建用户会话失败:', error);
      throw error;
    }
  }

  // 刷新用户会话
  private async refreshUserSession(userId: number, sessionId: string): Promise<void> {
    try {
      await this.env.DB.prepare(`
        UPDATE users
        SET last_active_time = ?,
            is_online = TRUE
        WHERE id = ? AND session_id = ?
      `).bind(Date.now(), userId, sessionId).run();

      console.log(`刷新用户 ${userId} 的会话: ${sessionId}`);
    } catch (error) {
      console.error('刷新用户会话失败:', error);
      throw error;
    }
  }

  // 验证会话ID（从数据库）
  private async validateSessionFromDB(sessionId: string, deviceId?: string): Promise<{
    valid: boolean;
    userId?: number;
    message?: string;
  }> {
    try {
      const result = await this.env.DB.prepare(`
        SELECT id, phone, device_id, last_active_time, is_online
        FROM users
        WHERE session_id = ?
      `).bind(sessionId).first();

      if (!result) {
        return { valid: false, message: '会话不存在' };
      }

      if (!result.is_online) {
        return { valid: false, message: '用户已离线' };
      }

      // 检查设备ID是否匹配（如果提供了）
      if (deviceId && String(result.device_id) !== deviceId) {
        return { valid: false, message: '设备不匹配' };
      }

      // 更新最后活跃时间
      await this.updateLastActiveTime(Number(result.id));

      return { valid: true, userId: Number(result.id) };

    } catch (error) {
      console.error('验证会话失败:', error);
      return { valid: false, message: '会话验证失败' };
    }
  }

  // 更新最后活跃时间
  private async updateLastActiveTime(userId: number): Promise<void> {
    try {
      await this.env.DB.prepare(`
        UPDATE users
        SET last_active_time = ?
        WHERE id = ?
      `).bind(Date.now(), userId).run();
    } catch (error) {
      console.error('更新活跃时间失败:', error);
    }
  }

  // 清理数据库中的用户会话
  private async clearUserSessionFromDB(userId: number): Promise<void> {
    try {
      await this.env.DB.prepare(`
        UPDATE users
        SET session_id = NULL,
            session_created_at = NULL,
            last_active_time = NULL,
            device_id = NULL,
            is_online = FALSE
        WHERE id = ?
      `).bind(userId).run();

      console.log(`清理用户 ${userId} 的数据库会话记录`);
    } catch (error) {
      console.error('清理数据库会话失败:', error);
    }
  }

  // 强制登出用户（从数据库）
  private async forceLogoutUserFromDB(userId: number, reason: string): Promise<void> {
    try {
      // 清理数据库中的会话
      await this.clearUserSessionFromDB(userId);

      // 通知SSE连接
      await this.notifyClientsForcedLogout(userId, reason);

      console.log(`强制登出用户 ${userId}: ${reason}`);
    } catch (error) {
      console.error('强制登出失败:', error);
    }
  }

  // ==================== 管理员会话管理方法（保持原有方式）====================

  private async loadSessions(): Promise<void> {
    if (this.initialized) return;
    try {
      const storedSessionsData = await this.ctx.storage.get<string>('sessions');
      const storedPhoneMapData = await this.ctx.storage.get<string>('phoneMap');
      const storedSessionIdMapData = await this.ctx.storage.get<string>('sessionIdMap');

      if (storedSessionsData) {
        const parsedSessions = JSON.parse(storedSessionsData);
        for (const userIdKey in parsedSessions) {
          const sessionData = parsedSessions[userIdKey];
          const numericUserId = parseInt(userIdKey, 10);
          if (!isNaN(numericUserId)) {
            this.sessions[numericUserId] = {
              ...sessionData,
              clientIds: new Set(sessionData.clientIds || [])
            };
          }
        }
        console.log(`已从存储中恢复会话数据: ${Object.keys(this.sessions).filter(k => k !== 'byPhone' && k !== 'bySessionId').length} 个用户会话`);
      }

      if (storedPhoneMapData) {
        this.sessions.byPhone = new Map(JSON.parse(storedPhoneMapData));
      } else {
        this.sessions.byPhone = new Map<string, number>();
      }

      if (storedSessionIdMapData) {
        this.sessions.bySessionId = new Map(JSON.parse(storedSessionIdMapData));
      } else {
        this.sessions.bySessionId = new Map<string, number>();
      }

    } catch (error) {
      console.error('从存储中加载会话数据失败:', error);
      this.sessions = {} as SessionManager;
      this.sessions.byPhone = new Map<string, number>();
      this.sessions.bySessionId = new Map<string, number>();
    }
    this.initialized = true;
  }

  private async saveSessions(): Promise<void> {
    if (!this.initialized) {
        await this.loadSessions();
    }
    try {
      const sessionsToStore: Record<string, any> = {};
      for (const userIdKey in this.sessions) {
        if (userIdKey === 'byPhone' || userIdKey === 'bySessionId') continue;
        const numericUserId = parseInt(userIdKey, 10);
        if (isNaN(numericUserId) || !this.sessions[numericUserId]) continue;
        const session = this.sessions[numericUserId];
        sessionsToStore[userIdKey] = {
          ...session,
          clientIds: Array.from(session.clientIds || [])
        };
      }
      const phoneEntries = Array.from(this.sessions.byPhone.entries());
      const sessionIdEntries = Array.from(this.sessions.bySessionId.entries());
      await this.ctx.storage.put('sessions', JSON.stringify(sessionsToStore));
      await this.ctx.storage.put('phoneMap', JSON.stringify(phoneEntries));
      await this.ctx.storage.put('sessionIdMap', JSON.stringify(sessionIdEntries));
      console.log('会话数据已保存到存储');
    } catch (error) {
      console.error('保存会话数据失败:', error);
    }
  }

  async fetch(request: Request): Promise<Response> {
    if (!this.initialized) { // 确保在处理任何请求前会话数据已加载
        await this.loadSessions();
    }

    const url = new URL(request.url);
    const path = url.pathname;
    console.log(`UserAuthDO 收到请求: ${request.method} ${path}`);

    // 先检查管理员端点（包括SSE）
    if (path === '/admin/sse') {
        console.log('收到管理员SSE连接请求');

        // 验证管理员SSE权限
        const url = new URL(request.url);
        const token = url.searchParams.get('token');

        if (!token) {
            console.log('管理员SSE连接缺少token');
            return new Response('Missing admin token', { status: 401 });
        }

        // 验证token
        const storedToken = await this.env.ADMIN_KV.get('admin_token');
        if (!storedToken || storedToken !== token) {
            console.log('管理员SSE token无效');
            return new Response('Invalid admin token', { status: 403 });
        }

        console.log('管理员SSE token验证成功');
        return this.handleAdminSSERequest(request);
    }

    // 处理批量进度推送
    if (path === '/admin/push-batch-progress') {
        if (request.method === 'POST') {
            return this.handlePushBatchProgress(request);
        }
        return new Response('Method Not Allowed for /admin/push-batch-progress', { status: 405 });
    }

    // 已移除 /admin/reset-alarm 路由，因为不再使用alarm机制
    if (path === '/admin/get-all-online-status') {
        // 为了保持向后兼容，暂时不验证管理员权限
        // TODO: 在前端实现完整的认证机制后，再启用严格验证

        // 批量获取所有用户在线状态
        if (request.method === 'POST') {
            return this.handleGetAllOnlineStatus(request);
        }
        return new Response('Method Not Allowed for /admin/get-all-online-status', { status: 405 });
    }

    // 检查用户SSE连接
    if (path === '/sse' || path === '/auth/sse') {
      return this.handleSSERequest(request);
    }

    switch (path) {
      case '/login':
        return this.handleLogin(request);
      case '/logout':
        return this.handleLogout(request);
      case '/check-session':
        return this.handleCheckSession(request);
      case '/check-user-online':
        return this.handleCheckUserOnline(request);
      case '/force-logout':
        return this.handleForceLogout(request);
      case '/notify-admin':
        return this.handleNotifyAdmin(request);
      case '/notify-user':
        return this.handleNotifyUser(request);
      case '/push-platform-data':
        return this.handlePushPlatformData(request);

      default:
        return new Response('Not Found in DO', { status: 404 });
    }
  }






  private async handleCheckUserOnline(request: Request): Promise<Response> {
    if (request.method !== 'POST') {
      return new Response('方法不允许', { status: 405 });
    }
    try {
      const { userId, phone } = await request.json<{ userId?: number; phone?: string }>();
      console.log(`检查用户在线状态: userId=${userId}, phone=${phone}`);

      // 从数据库查询用户在线状态
      let result = null;
      let targetUserId: number | undefined = userId;

      if (targetUserId) {
        // 通过用户ID查询
        result = await this.env.DB.prepare(`
          SELECT id, phone, is_online, session_id, last_active_time
          FROM users
          WHERE id = ?
        `).bind(targetUserId).first();
      } else if (phone) {
        // 通过手机号查询
        result = await this.env.DB.prepare(`
          SELECT id, phone, is_online, session_id, last_active_time
          FROM users
          WHERE phone = ?
        `).bind(phone).first();

        if (result) {
          targetUserId = Number(result.id);
        }
      } else {
        return new Response(JSON.stringify({ success: false, message: 'Either userId or phone must be provided.' }),
          { status: 400, headers: { 'Content-Type': 'application/json' } });
      }

      let isOnline = false;
      let hasSSEConnection = false;

      if (result) {
        // 检查数据库中的在线状态
        isOnline = Boolean(result.is_online) && Boolean(result.session_id);

        // 如果数据库显示在线，再检查是否有实际的SSE连接
        if (isOnline) {
          hasSSEConnection = await this.verifyUserOnlineBySSE(targetUserId!);

          // 如果没有SSE连接，更新数据库状态为离线
          if (!hasSSEConnection) {
            console.log(`用户 ${targetUserId} 数据库显示在线但无SSE连接，更新为离线状态`);
            await this.env.DB.prepare(`
              UPDATE users SET is_online = FALSE WHERE id = ?
            `).bind(targetUserId).run();
            isOnline = false;
          }
        }
      }

      console.log(`用户在线状态: ${isOnline ? '在线' : '离线'}, userId=${targetUserId}, hasSSEConnection=${hasSSEConnection}`);
      return new Response(JSON.stringify({
        success: true,
        online: isOnline,
        userId: targetUserId,
        hasSSEConnection: hasSSEConnection
      }), { headers: { 'Content-Type': 'application/json' } });
    } catch (error) {
      console.error('检查用户在线状态失败:', error);
      return new Response(JSON.stringify({ success: false, message: '检查用户在线状态失败' }),
        { status: 500, headers: { 'Content-Type': 'application/json' } });
    }
  }

  // 通过SSE连接验证用户真实在线状态
  private async verifyUserOnlineBySSE(userId: number): Promise<boolean> {
    try {
      console.log(`开始验证用户 ${userId} 的SSE在线状态`);

      // 查找用户的SSE连接
      let hasConnection = false;
      for (const [, connection] of this.sseConnections) {
        if (connection.userId === userId) {
          hasConnection = true;
          break;
        }
      }

      console.log(`用户 ${userId} SSE验证结果: ${hasConnection ? '在线' : '离线'}`);
      return hasConnection;

    } catch (error) {
      console.error(`SSE验证用户 ${userId} 失败:`, error);
      return false;
    }
  }



  // 批量获取所有用户在线状态（使用SSE连接验证）
  private async handleGetAllOnlineStatus(request: Request): Promise<Response> {
    try {
      const { userIds } = await request.json<{ userIds: number[] }>();
      console.log(`批量检查用户在线状态: userIds=${userIds?.join(',')}`);

      const onlineStatus: { [userId: number]: boolean } = {};

      if (userIds && Array.isArray(userIds)) {
        // 从数据库批量查询用户在线状态
        const placeholders = userIds.map(() => '?').join(',');
        const results = await this.env.DB.prepare(`
          SELECT id, is_online, session_id
          FROM users
          WHERE id IN (${placeholders})
        `).bind(...userIds).all();

        // 使用Promise.all并发验证所有用户的在线状态
        const verificationPromises = userIds.map(async (userId) => {
          const userResult = results.results.find(r => Number(r.id) === userId);

          if (userResult && userResult.is_online && userResult.session_id) {
            // 数据库显示在线，验证SSE连接
            const hasSSEConnection = await this.verifyUserOnlineBySSE(userId);

            if (!hasSSEConnection) {
              // 没有SSE连接，更新数据库状态为离线
              console.log(`批量检查：用户 ${userId} 无SSE连接，更新为离线状态`);
              await this.env.DB.prepare(`
                UPDATE users SET is_online = FALSE WHERE id = ?
              `).bind(userId).run();
              return { userId, isOnline: false };
            }

            return { userId, isOnline: true };
          } else {
            // 数据库显示离线或无会话
            return { userId, isOnline: false };
          }
        });

        const verificationResults = await Promise.all(verificationPromises);

        // 收集结果
        for (const result of verificationResults) {
          onlineStatus[result.userId] = result.isOnline;
        }
      }

      console.log(`批量在线状态检查完成，结果:`, onlineStatus);
      return new Response(JSON.stringify({ success: true, onlineStatus }),
        { headers: { 'Content-Type': 'application/json' } });
    } catch (error) {
      console.error('批量检查用户在线状态失败:', error);
      return new Response(JSON.stringify({ success: false, message: '批量检查用户在线状态失败' }),
        { status: 500, headers: { 'Content-Type': 'application/json' } });
    }
  }

  // 处理批量进度推送
  private async handlePushBatchProgress(request: Request): Promise<Response> {
    try {
      const progressMessage = await request.json();
      console.log('收到批量进度推送请求:', progressMessage);

      // 向所有管理员SSE连接推送进度
      let successfulPushes = 0;
      let failedPushes = 0;
      const totalAdminConnections = this.adminSSEConnections.size;

      if (totalAdminConnections === 0) {
        console.log('没有管理员SSE连接，跳过批量进度推送');
        return new Response(JSON.stringify({ success: true, message: '没有管理员在线，进度推送已忽略' }),
          { headers: { 'Content-Type': 'application/json' } });
      }

      const expiredConnections: string[] = [];

      for (const [connectionId, controller] of this.adminSSEConnections) {
        try {
          this.sendSSEMessage(controller, progressMessage);
          successfulPushes++;
          console.log(`成功向管理员推送批量进度: ${connectionId}`);
        } catch (error) {
          console.error(`向管理员推送批量进度失败: ${connectionId}`, error);
          failedPushes++;
          expiredConnections.push(connectionId);
        }
      }

      // 清理失效的连接
      for (const connectionId of expiredConnections) {
        this.adminSSEConnections.delete(connectionId);
      }

      return new Response(JSON.stringify({
        success: true,
        message: `批量进度已推送给 ${successfulPushes} 个管理员连接`,
        details: { successfulPushes, failedPushes, totalAdminConnections }
      }), { headers: { 'Content-Type': 'application/json' } });

    } catch (error) {
      console.error('处理批量进度推送失败:', error);
      return new Response(JSON.stringify({
        success: false,
        message: `批量进度推送失败: ${error}`
      }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }
  }

  // 处理管理员SSE连接
  private handleAdminSSERequest(request: Request): Response {
    console.log('管理员SSE连接请求');

    // 创建SSE流
    const stream = new ReadableStream({
      start: (controller) => {
        // 保存管理员连接
        const connectionId = crypto.randomUUID();
        this.adminSSEConnections.set(connectionId, controller);

        console.log(`管理员SSE连接已建立: ${connectionId}`);

        // 发送连接成功消息
        this.sendSSEMessage(controller, {
          type: 'connected',
          message: '管理员SSE连接已建立',
          timestamp: new Date().toISOString()
        });
      },

      cancel: () => {
        console.log('管理员SSE连接已断开');
      }
    });

    return new Response(stream, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Cache-Control'
      }
    });
  }



  // 向管理员推送用户状态变化（使用SSE）
  private async notifyAdminStatusChange(userId: number, phone: string, isOnline: boolean): Promise<void> {
    console.log(`准备推送用户状态变化: userId=${userId}, phone=${phone}, isOnline=${isOnline}`);

    const statusMessage = {
      type: 'user_status_change',
      data: {
        userId,
        phone,
        isOnline,
        timestamp: new Date().toISOString()
      }
    };

    let successfulPushes = 0;
    let failedPushes = 0;
    const totalAdminConnections = this.adminSSEConnections.size;

    if (totalAdminConnections === 0) {
      console.log('没有管理员SSE连接，跳过状态推送');
      return;
    }

    // 向所有管理员SSE连接推送状态变化
    const expiredConnections: string[] = [];

    for (const [connectionId, controller] of this.adminSSEConnections) {
      try {
        this.sendSSEMessage(controller, statusMessage);
        successfulPushes++;
        console.log(`成功向管理员SSE推送状态变化: ${connectionId}`);
      } catch (error) {
        console.error(`向管理员SSE推送失败: ${connectionId}`, error);
        failedPushes++;
        expiredConnections.push(connectionId);
      }
    }

    // 清理失效的连接
    for (const connectionId of expiredConnections) {
      this.adminSSEConnections.delete(connectionId);
    }

    console.log(`管理员状态推送完成: 总管理员连接数=${totalAdminConnections}, 成功推送=${successfulPushes}, 失败=${failedPushes}, userId=${userId}, phone=${phone}, isOnline=${isOnline}`);
  }

  // 处理用户SSE连接
  private handleSSERequest(request: Request): Response {
    const url = new URL(request.url);
    const sessionId = url.searchParams.get('sessionId');
    const deviceId = url.searchParams.get('deviceId');
    const clientId = url.searchParams.get('clientId') || crypto.randomUUID();

    console.log(`用户SSE连接请求: sessionId=${sessionId}, deviceId=${deviceId}, clientId=${clientId}`);

    if (!sessionId) {
      return new Response('需要会话ID', { status: 400 });
    }

    if (!deviceId) {
      return new Response('需要设备ID', { status: 400 });
    }

    // 异步验证会话，但立即返回流
    const stream = new ReadableStream({
      start: async (controller) => {
        try {
          // 验证会话
          const validation = await this.validateSessionFromDB(sessionId, deviceId);

          if (!validation.valid) {
            console.log(`用户SSE连接验证失败: ${validation.message}`);
            this.sendSSEMessage(controller, {
              type: 'error',
              message: validation.message
            });
            controller.close();
            return;
          }

          const userId = validation.userId!;
          const connectionId = `${userId}_${clientId}`;

          // 保存SSE连接
          this.sseConnections.set(connectionId, {
            controller,
            userId,
            clientId,
            sessionId,
            connectedAt: Date.now()
          });

          // 发送连接成功消息
          this.sendSSEMessage(controller, {
            type: 'connected',
            message: 'SSE连接已建立',
            timestamp: new Date().toISOString()
          });

        } catch (error) {
          console.error('用户SSE连接处理失败:', error);
          this.sendSSEMessage(controller, {
            type: 'error',
            message: '连接处理失败'
          });
          controller.close();
        }
      },

      cancel: () => {
        // 连接断开时的清理
        const connectionId = `${sessionId}_${clientId}`;
        this.sseConnections.delete(connectionId);
        console.log(`用户SSE连接已断开: ${connectionId}`);
      }
    });

    return new Response(stream, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Cache-Control'
      }
    });
  }







  private async handleLogin(request: Request): Promise<Response> {
    if (request.method !== 'POST') { return new Response('方法不允许', { status: 405 }); }
    try {
      const { userId, phone, forceLogin = false, completeUserInfo, deviceId } = await request.json<{
        userId: number;
        phone: string;
        forceLogin?: boolean;
        completeUserInfo?: any;
        deviceId?: string;
      }>();

      if (!userId || !phone) {
        return new Response(JSON.stringify({ success: false, message: 'User ID 和 phone 是必需的。' }),
          { status: 400, headers: { 'Content-Type': 'application/json' } });
      }

      const finalDeviceId = deviceId || `device_${Date.now()}_${Math.random().toString(36).substring(2)}`;

      console.log(`处理用户登录请求: userId=${userId}, phone=${phone}, deviceId=${finalDeviceId}, forceLogin=${forceLogin}`);
      // 检查数据库中是否已有活跃会话
      const existingSession = await this.checkExistingSession(userId, phone);

      if (existingSession.hasActiveSession) {
        if (existingSession.deviceId === finalDeviceId) {
          // 同一设备重新登录，刷新会话
          console.log(`同一设备 ${finalDeviceId} 重新登录，刷新会话`);
          await this.refreshUserSession(userId, existingSession.sessionId!);

          return new Response(JSON.stringify({
            success: true,
            message: '会话已刷新',
            sessionId: existingSession.sessionId,
            deviceId: finalDeviceId
          }), { headers: { 'Content-Type': 'application/json' } });

        } else if (!forceLogin) {
          // 不同设备尝试登录
          console.log(`用户 ${userId} 已在设备 ${existingSession.deviceId} 上登录`);
          return new Response(JSON.stringify({
            success: false,
            message: '账号已在其他设备登录，每个账号只能在一台设备上使用',
            requireForceLogin: true,
            currentDevice: existingSession.deviceId
          }), { headers: { 'Content-Type': 'application/json' } });

        } else {
          // 强制登录，踢掉现有设备
          console.log(`强制登录：踢掉设备 ${existingSession.deviceId}，允许设备 ${finalDeviceId} 登录`);
          await this.forceLogoutUserFromDB(userId, '您的账号已在另一台设备上登录');
        }
      }
      // 创建新会话
      const newSessionId = crypto.randomUUID();
      await this.createUserSession(userId, phone, newSessionId, finalDeviceId);

      console.log(`用户 ${userId} 在设备 ${finalDeviceId} 上登录成功，会话ID: ${newSessionId}`);

      const loginResponse: any = {
        success: true,
        message: '登录成功',
        sessionId: newSessionId,
        deviceId: finalDeviceId
      };

      if (completeUserInfo) {
        loginResponse.user = completeUserInfo;
      }

      return new Response(JSON.stringify(loginResponse),
        { headers: { 'Content-Type': 'application/json' } });
    } catch (error) {
      console.error('登录失败:', error);
      return new Response(JSON.stringify({ success: false, message: '登录处理失败' }),
        { status: 500, headers: { 'Content-Type': 'application/json' } });
    }
  }

  private async handleLogout(request: Request): Promise<Response> {
    if (request.method !== 'POST') { return new Response('方法不允许', { status: 405 }); }
    try {
      const { sessionId } = await request.json<{ sessionId: string }>();
      if (!sessionId) {
        return new Response(JSON.stringify({
          success: false,
          message: 'Session ID 是必需的。'
        }), { status: 400, headers: { 'Content-Type': 'application/json' } });
      }

      console.log(`处理用户登出请求: sessionId=${sessionId}`);

      // 从数据库获取用户ID
      const result = await this.env.DB.prepare(`
        SELECT id FROM users WHERE session_id = ?
      `).bind(sessionId).first();

      if (result) {
        const userId = Number(result.id);
        await this.clearUserSessionFromDB(userId);
        await this.notifyClientsForcedLogout(userId, '您已成功退出登录');
      }

      return new Response(JSON.stringify({
        success: true,
        message: '登出成功'
      }), { headers: { 'Content-Type': 'application/json' } });
    } catch (error) {
      console.error('登出失败:', error);
      return new Response(JSON.stringify({ success: false, message: '登出处理失败' }),
        { status: 500, headers: { 'Content-Type': 'application/json' } });
    }
  }

  private async handleCheckSession(request: Request): Promise<Response> {
    if (request.method !== 'POST') { return new Response('方法不允许', { status: 405 }); }
    try {
      const { sessionId, deviceId } = await request.json<{
        sessionId: string;
        deviceId?: string;
      }>();

      if (!sessionId) {
        return new Response(JSON.stringify({
          valid: false,
          message: 'Session ID 是必需的。'
        }), { status: 400, headers: { 'Content-Type': 'application/json' } });
      }

      console.log(`检查用户会话: sessionId=${sessionId}, deviceId=${deviceId}`);

      const validation = await this.validateSessionFromDB(sessionId, deviceId);

      if (validation.valid) {
        return new Response(JSON.stringify({
          valid: true,
          userId: validation.userId
        }), { headers: { 'Content-Type': 'application/json' } });
      } else {
        return new Response(JSON.stringify({
          valid: false,
          message: validation.message
        }), { headers: { 'Content-Type': 'application/json' } });
      }

    } catch (error) {
      console.error('检查用户会话失败:', error);
      return new Response(JSON.stringify({
        valid: false,
        message: '检查会话请求处理失败'
      }), { status: 500, headers: { 'Content-Type': 'application/json' } });
    }
  }

  private async notifyClientsForcedLogout(userId: number, reason: string): Promise<void> {
    const session = this.sessions[userId];
    console.log(`通知客户端强制登出: userId=${userId}, reason='${reason}', 内存中会话的 clientIds 数量 (如果存在): ${session ? session.clientIds.size : 'N/A'}`);

    let notifiedCount = 0;
    const expiredConnections: string[] = [];

    // 向用户的所有SSE连接发送强制登出消息
    for (const [connectionId, connection] of this.sseConnections) {
      if (connection.userId === userId) {
        try {
          console.log(`通知强制登出: 发送给 userId=${userId}, clientId=${connection.clientId}`);

          // 发送强制登出消息
          this.sendSSEMessage(connection.controller, {
            type: 'force_logout',
            message: reason,
            timestamp: new Date().toISOString()
          });

          notifiedCount++;

          // 标记连接为过期，稍后清理
          expiredConnections.push(connectionId);

          // 从内存会话中清理
          if (session) {
            session.clientIds.delete(connection.clientId);
          }
        } catch (error) {
          console.error(`通知强制登出: 处理 userId=${userId} 的 SSE连接时出错:`, error);
          expiredConnections.push(connectionId);
        }
      }
    }

    // 清理被强制登出的连接
    for (const connectionId of expiredConnections) {
      this.sseConnections.delete(connectionId);
    }



    // 如果 session.clientIds 被修改了，需要保存
    if (session && notifiedCount > 0) {
      await this.saveSessions();
    }
  }

  private async clearUserSession(userId: number, shouldSave: boolean = true, notifyAdmin: boolean = true): Promise<void> {
    const session = this.sessions[userId];
    if (!session) {
      return;
    }

    const userPhone = session.phone;

    this.sessions.byPhone.delete(session.phone);
    this.sessions.bySessionId.delete(session.sessionId);
    delete this.sessions[userId]; // 从主对象中删除

    // 推送用户下线状态给管理员（如果需要）
    if (notifyAdmin) {
      await this.notifyAdminStatusChange(userId, userPhone, false);
    }

    // 持久化存储中的 'sessions', 'phoneMap', 'sessionIdMap' 会在下次 saveSessions 时被更新的状态覆盖
    // 如果希望立即删除这些key（如果这是最后一个用户被清除），需要更复杂的逻辑
    // 通常，让 saveSessions() 用空内容（或减少后的内容）覆盖是可以接受的
    if (shouldSave) {
        await this.saveSessions();
        console.log(`已清除 userId=${userId} 的用户会话并保存了更新后的会话数据。`);
    } else {
        console.log(`已从内存中清除 userId=${userId} 的用户会话。保存已推迟。`);
    }
  }

  private async handleForceLogout(request: Request): Promise<Response> {
    if (request.method !== 'POST') { return new Response('方法不允许', { status: 405 }); }
    try {
      const { userId, phone } = await request.json<{ userId?: number; phone?: string }>();
      console.log(`处理强制下线请求: userId=${userId}, phone=${phone}`);
      let targetUserId: number | undefined = userId;
      if (!targetUserId && phone) {
        targetUserId = this.sessions.byPhone.get(phone);
      }
      if (!targetUserId || !this.sessions[targetUserId]) { // 确保目标用户存在于会话中
        console.log(`强制下线: 未找到用户或用户不在线。提供的 userId=${userId}, phone=${phone}. 解析后的 targetUserId=${targetUserId}`);
        return new Response(JSON.stringify({ success: false, message: '用户不在线或未找到' }),
          { headers: { 'Content-Type': 'application/json' } });
      }
      console.log(`正在为 userId=${targetUserId} 执行强制下线`);

      // 获取用户信息用于推送状态变化
      const userSession = this.sessions[targetUserId];
      const userPhone = userSession.phone;

      await this.notifyClientsForcedLogout(targetUserId, '您已被管理员强制下线');

      // 推送用户下线状态给管理员
      await this.notifyAdminStatusChange(targetUserId, userPhone, false);

      await this.clearUserSession(targetUserId, true, false); // 清理并保存，但不重复推送给管理员
      return new Response(JSON.stringify({ success: true, message: '用户已被强制下线', userId: targetUserId }),
        { headers: { 'Content-Type': 'application/json' } });
    } catch (error) {
      console.error('强制下线处理失败:', error);
      return new Response(JSON.stringify({ success: false, message: '强制下线处理失败' }),
        { status: 500, headers: { 'Content-Type': 'application/json' } });
    }
  }

  // 处理向管理员推送通知的请求
  private async handleNotifyAdmin(request: Request): Promise<Response> {
    try {
      if (request.method !== 'POST') {
        return new Response('Method Not Allowed', { status: 405 });
      }

      const { type, message, data } = await request.json<{
        type: string;
        message: string;
        data?: any;
      }>();

      if (!type || !message) {
        return new Response(JSON.stringify({ success: false, message: '缺少必要参数' }),
          { status: 400, headers: { 'Content-Type': 'application/json' } });
      }

      // 向所有管理员SSE连接推送通知
      const notificationMessage = {
        type: type,
        message: message,
        data: data,
        timestamp: new Date().toISOString()
      };

      let successfulPushes = 0;
      let failedPushes = 0;
      const totalAdminConnections = this.adminSSEConnections.size;

      if (totalAdminConnections === 0) {
        console.log('没有管理员SSE连接，跳过通知推送');
        return new Response(JSON.stringify({ success: true, message: '没有管理员在线，通知已忽略' }),
          { headers: { 'Content-Type': 'application/json' } });
      }

      // 向所有管理员SSE连接推送通知
      const expiredConnections: string[] = [];

      for (const [connectionId, controller] of this.adminSSEConnections) {
        try {
          this.sendSSEMessage(controller, notificationMessage);
          successfulPushes++;
          console.log(`成功向管理员推送通知: ${connectionId}`);
        } catch (error) {
          console.error(`向管理员推送通知失败: ${connectionId}`, error);
          failedPushes++;
          expiredConnections.push(connectionId);
        }
      }

      // 清理失效的连接
      for (const connectionId of expiredConnections) {
        this.adminSSEConnections.delete(connectionId);
      }



      return new Response(JSON.stringify({
        success: true,
        message: `通知已推送给 ${successfulPushes} 个管理员连接`,
        details: { successfulPushes, failedPushes, totalAdminConnections }
      }), { headers: { 'Content-Type': 'application/json' } });

    } catch (error) {
      console.error('处理管理员通知推送失败:', error);
      return new Response(JSON.stringify({ success: false, message: '推送处理失败' }),
        { status: 500, headers: { 'Content-Type': 'application/json' } });
    }
  }

  // 处理向特定用户推送通知的请求
  private async handleNotifyUser(request: Request): Promise<Response> {
    try {
      if (request.method !== 'POST') {
        return new Response('Method Not Allowed', { status: 405 });
      }

      const requestBody = await request.json<{
        targetUserId?: number;
        userId?: number;
        type?: string;
        message?: string;
        data?: any;
        notification?: {
          type: string;
          message: string;
          data?: any;
        };
      }>();

      // 支持两种格式：
      // 1. { targetUserId, type, message, data }
      // 2. { userId, notification: { type, message, data } }
      let targetUserId: number;
      let type: string;
      let message: string;
      let data: any;

      if (requestBody.notification) {
        // 新格式
        targetUserId = requestBody.userId!;
        type = requestBody.notification.type;
        message = requestBody.notification.message;
        data = requestBody.notification.data;
      } else {
        // 旧格式
        targetUserId = requestBody.targetUserId!;
        type = requestBody.type!;
        message = requestBody.message!;
        data = requestBody.data;
      }

      if (!targetUserId || !type || !message) {
        console.log('推送参数检查失败:', { targetUserId, type, message, requestBody });
        return new Response(JSON.stringify({ success: false, message: '缺少必要参数' }),
          { status: 400, headers: { 'Content-Type': 'application/json' } });
      }

      // 检查目标用户是否在线
      const userSession = this.sessions[targetUserId];
      if (!userSession || userSession.clientIds.size === 0) {
        console.log(`用户 ${targetUserId} 不在线，跳过通知推送`);
        return new Response(JSON.stringify({ success: true, message: '用户不在线，通知已忽略' }),
          { headers: { 'Content-Type': 'application/json' } });
      }

      // 准备推送消息
      const notificationMessage = {
        type: type,
        message: message,
        data: data,
        timestamp: new Date().toISOString()
      };

      let successfulPushes = 0;
      let failedPushes = 0;
      const expiredConnections: string[] = [];

      // 向用户的所有SSE连接推送通知
      for (const [connectionId, connection] of this.sseConnections) {
        if (connection.userId === targetUserId) {
          try {
            this.sendSSEMessage(connection.controller, notificationMessage);
            successfulPushes++;
            console.log(`成功向用户 ${targetUserId} 推送通知: ${connectionId}`);
          } catch (error) {
            console.error(`向用户 ${targetUserId} 推送通知失败: ${connectionId}`, error);
            failedPushes++;
            expiredConnections.push(connectionId);
          }
        }
      }

      // 清理失效的连接
      for (const connectionId of expiredConnections) {
        this.sseConnections.delete(connectionId);
        // 同时更新用户会话中的客户端ID
        if (userSession) {
          const clientId = connectionId.split('_')[1];
          userSession.clientIds.delete(clientId);
        }
      }

      if (expiredConnections.length > 0) {
        this.saveSessions();
      }

      console.log(`用户通知推送完成: userId=${targetUserId}, 成功=${successfulPushes}, 失败=${failedPushes}`);

      return new Response(JSON.stringify({
        success: true,
        message: `通知已推送给用户 ${targetUserId} 的 ${successfulPushes} 个连接`,
        details: { targetUserId, successfulPushes, failedPushes }
      }), { headers: { 'Content-Type': 'application/json' } });

    } catch (error) {
      console.error('处理用户通知推送失败:', error);
      return new Response(JSON.stringify({ success: false, message: '推送处理失败' }),
        { status: 500, headers: { 'Content-Type': 'application/json' } });
    }
  }

  // 处理平台数据推送请求
  private async handlePushPlatformData(request: Request): Promise<Response> {
    try {
      if (request.method !== 'POST') {
        return new Response('Method Not Allowed', { status: 405 });
      }

      const requestBody = await request.json<{
        type: string;
        target: 'admin' | 'user';
        mainAccountId?: number;
        data: any;
      }>();

      const { type, target, mainAccountId, data } = requestBody;

      if (!type || !target || !data) {
        return new Response(JSON.stringify({ success: false, message: '缺少必要参数' }),
          { status: 400, headers: { 'Content-Type': 'application/json' } });
      }

      if (target === 'admin') {
        // 推送给所有管理员
        return await this.pushToAdmins(type, data);
      } else if (target === 'user' && mainAccountId) {
        // 推送给特定主账号及其子账号
        return await this.pushToMainAccountUsers(mainAccountId, type, data);
      } else {
        return new Response(JSON.stringify({ success: false, message: '无效的推送目标' }),
          { status: 400, headers: { 'Content-Type': 'application/json' } });
      }

    } catch (error) {
      console.error('处理平台数据推送失败:', error);
      return new Response(JSON.stringify({ success: false, message: '推送处理失败' }),
        { status: 500, headers: { 'Content-Type': 'application/json' } });
    }
  }

  // 推送给所有管理员
  private async pushToAdmins(type: string, data: any): Promise<Response> {
    try {
      console.log('开始推送平台数据给所有管理员...');

      const pushMessage = {
        type: type,
        data: data,
        timestamp: new Date().toISOString()
      };

      let successfulPushes = 0;
      let failedPushes = 0;
      const totalAdminConnections = this.adminSSEConnections.size;

      if (totalAdminConnections === 0) {
        console.log('没有管理员在线，跳过推送');
        return new Response(JSON.stringify({ success: true, message: '没有管理员在线，推送已忽略' }),
          { headers: { 'Content-Type': 'application/json' } });
      }

      // 向所有管理员SSE连接推送平台数据
      const expiredConnections: string[] = [];

      for (const [connectionId, controller] of this.adminSSEConnections) {
        try {
          this.sendSSEMessage(controller, pushMessage);
          successfulPushes++;
          console.log(`成功向管理员推送平台数据: ${connectionId}`);
        } catch (error) {
          console.error(`向管理员推送平台数据失败: ${connectionId}`, error);
          failedPushes++;
          expiredConnections.push(connectionId);
        }
      }

      // 清理失效的连接
      for (const connectionId of expiredConnections) {
        this.adminSSEConnections.delete(connectionId);
      }

      return new Response(JSON.stringify({
        success: true,
        message: `平台数据已推送给 ${successfulPushes} 个管理员连接`,
        details: { successfulPushes, failedPushes, totalAdminConnections }
      }), { headers: { 'Content-Type': 'application/json' } });

    } catch (error) {
      console.error('推送平台数据给管理员失败:', error);
      return new Response(JSON.stringify({ success: false, message: '推送处理失败' }),
        { status: 500, headers: { 'Content-Type': 'application/json' } });
    }
  }

  // 推送给特定主账号及其子账号
  private async pushToMainAccountUsers(mainAccountId: number, type: string, data: any): Promise<Response> {
    try {
      console.log(`开始推送平台数据给主账号 ${mainAccountId} 及其子账号...`);

      const pushMessage = {
        type: type,
        data: data,
        timestamp: new Date().toISOString()
      };

      let successfulPushes = 0;
      let failedPushes = 0;
      const expiredConnections: string[] = [];

      // 查找主账号和子账号的用户ID
      const targetUserIds = await this.findUserIdsByMainAccount(mainAccountId);

      if (targetUserIds.length === 0) {
        console.log(`主账号 ${mainAccountId} 及其子账号都不在线`);
        return new Response(JSON.stringify({ success: true, message: '目标用户都不在线，推送已忽略' }),
          { headers: { 'Content-Type': 'application/json' } });
      }

      // 向目标用户的所有SSE连接推送平台数据
      for (const [connectionId, connection] of this.sseConnections) {
        if (targetUserIds.includes(connection.userId)) {
          try {
            this.sendSSEMessage(connection.controller, pushMessage);
            successfulPushes++;
            console.log(`成功向用户 ${connection.userId} 推送平台数据: ${connectionId}`);
          } catch (error) {
            console.error(`向用户 ${connection.userId} 推送平台数据失败: ${connectionId}`, error);
            failedPushes++;
            expiredConnections.push(connectionId);
          }
        }
      }

      // 清理失效的连接
      for (const connectionId of expiredConnections) {
        this.sseConnections.delete(connectionId);
      }

      return new Response(JSON.stringify({
        success: true,
        message: `平台数据已推送给主账号 ${mainAccountId} 的 ${successfulPushes} 个用户连接`,
        details: { mainAccountId, targetUserIds, successfulPushes, failedPushes }
      }), { headers: { 'Content-Type': 'application/json' } });

    } catch (error) {
      console.error(`推送平台数据给主账号 ${mainAccountId} 失败:`, error);
      return new Response(JSON.stringify({ success: false, message: '推送处理失败' }),
        { status: 500, headers: { 'Content-Type': 'application/json' } });
    }
  }

  // 根据主账号ID查找相关的用户ID（主账号和子账号）
  private async findUserIdsByMainAccount(mainAccountId: number): Promise<number[]> {
    try {
      const userIds: number[] = [];

      // 遍历所有在线用户，查找主账号ID匹配的用户
      for (const [userId, session] of Object.entries(this.sessions)) {
        if (typeof session === 'object' && session.clientIds && session.clientIds.size > 0) {
          // 这里需要根据实际的用户-主账号关系来判断
          // 暂时简化处理，实际应该查询数据库获取用户的主账号关系
          // 假设用户ID就是主账号ID，或者有某种关联关系
          const numericUserId = parseInt(userId);
          if (numericUserId === mainAccountId) {
            userIds.push(numericUserId);
          }
          // TODO: 添加查找子账号的逻辑
        }
      }

      console.log(`主账号 ${mainAccountId} 找到 ${userIds.length} 个在线用户:`, userIds);
      return userIds;

    } catch (error) {
      console.error(`查找主账号 ${mainAccountId} 相关用户失败:`, error);
      return [];
    }
  }
}