-- 为用户表添加会话管理字段
-- 这些字段用于存储用户的会话信息，避免Durable Object重启导致的会话丢失

-- 添加会话相关字段
ALTER TABLE users ADD COLUMN session_id TEXT;
ALTER TABLE users ADD COLUMN session_created_at INTEGER;
ALTER TABLE users ADD COLUMN last_active_time INTEGER;
ALTER TABLE users ADD COLUMN device_id TEXT;
ALTER TABLE users ADD COLUMN is_online BOOLEAN DEFAULT FALSE;

-- 创建索引提高查询性能
CREATE INDEX IF NOT EXISTS idx_users_session_id ON users(session_id);
CREATE INDEX IF NOT EXISTS idx_users_device_id ON users(device_id);
CREATE INDEX IF NOT EXISTS idx_users_online_status ON users(is_online);
CREATE INDEX IF NOT EXISTS idx_users_last_active ON users(last_active_time);

-- 添加注释说明字段用途
COMMENT ON COLUMN users.session_id IS '用户会话ID，用于身份验证';
COMMENT ON COLUMN users.session_created_at IS '会话创建时间戳';
COMMENT ON COLUMN users.last_active_time IS '最后活跃时间戳';
COMMENT ON COLUMN users.device_id IS '设备ID，用于单设备登录控制';
COMMENT ON COLUMN users.is_online IS '用户在线状态';
