# 用户会话数据库存储方案（简化版）

## 概述

为了解决Durable Object重启导致的用户会话丢失问题，我们在原有内存存储的基础上，增加数据库存储作为备份。保持所有原有逻辑不变，只是增加数据库同步。

## 架构变更

### 用户会话管理（混合方案）
- **主存储**：Durable Object内存（保持原有逻辑）
- **备份存储**：D1数据库的users表（新增session_id字段）
- **验证方式**：优先使用内存，内存丢失时从数据库恢复
- **优势**：保持原有性能，解决重启丢失问题

### 管理员会话管理（保持不变）
- **存储位置**：Durable Object内存
- **会话ID**：随机UUID
- **验证方式**：内存查询验证

## 数据库字段

在users表中只添加一个字段：

```sql
-- 会话ID字段
session_id TEXT               -- 用户会话ID备份
```

## 核心功能

### 1. 登录流程（保持原有逻辑）
```typescript
// 检查内存中的现有会话（原有逻辑）
const existingUserIdByPhone = this.sessions.byPhone.get(phone);
if (existingUserIdByPhone && this.sessions[existingUserIdByPhone]) {
  // 验证SSE连接状态
  const isReallyOnline = await this.verifyUserOnlineBySSE(existingUserIdByPhone);

  if (!isReallyOnline) {
    await this.clearUserSession(existingUserIdByPhone, true);
  } else if (!forceLogin) {
    return { requireForceLogin: true };
  } else {
    await this.clearUserSession(existingUserIdByPhone, false);
  }
}

// 创建新会话（原有逻辑 + 数据库备份）
const sessionId = crypto.randomUUID();
this.sessions[userId] = { userId, phone, sessionId, ... };
this.sessions.byPhone.set(phone, userId);
this.sessions.bySessionId.set(sessionId, userId);

// 新增：同时保存到数据库
await this.saveSessionToDatabase(userId, sessionId);
await this.saveSessions();
```

### 2. 会话验证（保持原有逻辑）
```typescript
// 优先检查内存
const userId = this.sessions.bySessionId.get(sessionId);
const userSession = userId ? this.sessions[userId] : undefined;

if (!userSession) {
  // 内存中没有，尝试从数据库恢复
  const dbUserId = await this.getUserIdBySessionId(sessionId);
  if (dbUserId) {
    // 数据库中有但内存中没有，要求重新登录
    await this.clearSessionFromDatabase(dbUserId);
  }
  return { valid: false, message: '会话无效或已过期' };
}

// 更新活跃时间
userSession.lastActiveTime = Date.now();
```

### 3. SSE连接（保持原有逻辑）
```typescript
// 检查内存中的会话
const userId = this.sessions.bySessionId.get(sessionId);
if (!userId || !this.sessions[userId]) {
  return new Response('会话无效或已过期', { status: 401 });
}

// 建立SSE连接（原有逻辑）
this.sseConnections.set(connectionId, { controller, userId, clientId, sessionId });
this.sessions[userId].clientIds.add(clientId);
```

### 4. 会话清理（原有逻辑 + 数据库清理）
```typescript
// 清理内存会话（原有逻辑）
this.sessions.byPhone.delete(session.phone);
this.sessions.bySessionId.delete(session.sessionId);
delete this.sessions[userId];

// 新增：同时清理数据库
await this.clearSessionFromDatabase(userId);
```

## 优势分析

### ✅ 解决会话丢失问题
- **持久化存储**：会话存储在数据库中，不会因为DO重启而丢失
- **自动恢复**：DO重启后可以从数据库中验证会话状态
- **数据一致性**：数据库保证数据的持久性和一致性

### ✅ 保持功能完整性
- **单设备登录**：通过device_id字段控制
- **强制登出**：通过清理数据库记录实现
- **活跃时间跟踪**：通过last_active_time字段记录
- **在线状态**：通过is_online字段跟踪

### ✅ 性能优化
- **减少内存使用**：只存储SSE连接，不存储会话映射
- **数据库索引**：为常用查询字段添加索引
- **批量操作**：支持批量会话管理

### ✅ 运维友好
- **可查询**：可以通过SQL查询用户会话状态
- **可管理**：管理员可以直接操作数据库管理会话
- **可监控**：可以统计在线用户数、会话时长等

## 前端适配

前端需要在请求中包含deviceId：

```javascript
// 登录时
const response = await fetch(`${API_URL}/auth/login`, {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    userId,
    phone,
    password,
    deviceId: localStorage.getItem('deviceId') || generateDeviceId(),
    forceLogin
  })
});

// SSE连接时
const sseUrl = `${API_URL}/auth/sse?sessionId=${sessionId}&deviceId=${deviceId}&clientId=${clientId}`;

// 会话检查时
const response = await fetch(`${API_URL}/auth/check-session`, {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ sessionId, deviceId })
});
```

## 部署步骤

1. **执行数据库迁移**：
   ```bash
   # 执行SQL脚本添加字段
   wrangler d1 execute your-database --file=database/add_session_fields.sql
   ```

2. **部署新代码**：
   ```bash
   npm run deploy
   ```

3. **验证功能**：
   - 测试用户登录/登出
   - 测试SSE连接
   - 测试会话验证
   - 测试单设备登录限制

## 注意事项

1. **数据库性能**：确保为session_id等字段添加了索引
2. **错误处理**：处理数据库连接失败的情况
3. **兼容性**：确保前端传递正确的deviceId参数
4. **会话管理**：会话不会自动过期，需要用户主动登出或强制登出

这种方案有效解决了会话丢失问题，同时保持了所有现有功能的完整性。
