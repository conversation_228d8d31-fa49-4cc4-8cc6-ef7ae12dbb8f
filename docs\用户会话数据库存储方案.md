# 用户会话数据库存储方案

## 概述

为了解决Durable Object重启导致的用户会话丢失问题，我们将用户会话信息从内存存储改为数据库存储。管理员会话仍然保持原有的内存存储方式。

## 架构变更

### 用户会话管理（新方案）
- **存储位置**：D1数据库的users表
- **会话ID**：仍然使用随机UUID
- **验证方式**：从数据库查询验证
- **优势**：不会因为DO重启而丢失

### 管理员会话管理（保持不变）
- **存储位置**：Durable Object内存
- **会话ID**：随机UUID
- **验证方式**：内存查询验证
- **说明**：管理员使用频率低，保持原有方式

## 数据库字段

在users表中添加以下字段：

```sql
-- 会话相关字段
session_id TEXT,              -- 会话ID
session_created_at INTEGER,   -- 会话创建时间
last_active_time INTEGER,     -- 最后活跃时间
device_id TEXT,               -- 设备ID
is_online BOOLEAN DEFAULT FALSE -- 在线状态
```

## 核心功能

### 1. 登录流程
```typescript
// 检查现有会话
const existingSession = await this.checkExistingSession(userId, phone);

if (existingSession.hasActiveSession) {
  if (existingSession.deviceId === deviceId) {
    // 同设备重新登录，刷新会话
    await this.refreshUserSession(userId, existingSession.sessionId);
  } else if (!forceLogin) {
    // 不同设备，需要强制登录
    return { requireForceLogin: true };
  } else {
    // 强制登录，踢掉现有设备
    await this.forceLogoutUserFromDB(userId, reason);
  }
}

// 创建新会话
const sessionId = crypto.randomUUID();
await this.createUserSession(userId, phone, sessionId, deviceId);
```

### 2. 会话验证
```typescript
const validation = await this.validateSessionFromDB(sessionId, deviceId);

if (!validation.valid) {
  return { error: validation.message };
}

// 自动更新最后活跃时间
await this.updateLastActiveTime(validation.userId);
```

### 3. SSE连接
```typescript
// 异步验证会话
const validation = await this.validateSessionFromDB(sessionId, deviceId);

if (!validation.valid) {
  // 发送错误消息并关闭连接
  this.sendSSEMessage(controller, { type: 'error', message: validation.message });
  controller.close();
  return;
}

// 建立SSE连接
this.sseConnections.set(connectionId, { controller, userId, clientId, sessionId });
```

### 4. 会话清理
```typescript
// 登出时清理
await this.clearUserSessionFromDB(userId);

// 强制登出时清理
await this.forceLogoutUserFromDB(userId, reason);
```

## 优势分析

### ✅ 解决会话丢失问题
- **持久化存储**：会话存储在数据库中，不会因为DO重启而丢失
- **自动恢复**：DO重启后可以从数据库中验证会话状态
- **数据一致性**：数据库保证数据的持久性和一致性

### ✅ 保持功能完整性
- **单设备登录**：通过device_id字段控制
- **强制登出**：通过清理数据库记录实现
- **活跃时间跟踪**：通过last_active_time字段记录
- **在线状态**：通过is_online字段跟踪

### ✅ 性能优化
- **减少内存使用**：只存储SSE连接，不存储会话映射
- **数据库索引**：为常用查询字段添加索引
- **批量操作**：支持批量会话管理

### ✅ 运维友好
- **可查询**：可以通过SQL查询用户会话状态
- **可管理**：管理员可以直接操作数据库管理会话
- **可监控**：可以统计在线用户数、会话时长等

## 前端适配

前端需要在请求中包含deviceId：

```javascript
// 登录时
const response = await fetch(`${API_URL}/auth/login`, {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    userId,
    phone,
    password,
    deviceId: localStorage.getItem('deviceId') || generateDeviceId(),
    forceLogin
  })
});

// SSE连接时
const sseUrl = `${API_URL}/auth/sse?sessionId=${sessionId}&deviceId=${deviceId}&clientId=${clientId}`;

// 会话检查时
const response = await fetch(`${API_URL}/auth/check-session`, {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ sessionId, deviceId })
});
```

## 部署步骤

1. **执行数据库迁移**：
   ```bash
   # 执行SQL脚本添加字段
   wrangler d1 execute your-database --file=database/add_session_fields.sql
   ```

2. **部署新代码**：
   ```bash
   npm run deploy
   ```

3. **验证功能**：
   - 测试用户登录/登出
   - 测试SSE连接
   - 测试会话验证
   - 测试单设备登录限制

## 注意事项

1. **数据库性能**：确保为session_id等字段添加了索引
2. **错误处理**：处理数据库连接失败的情况
3. **兼容性**：确保前端传递正确的deviceId参数
4. **会话管理**：会话不会自动过期，需要用户主动登出或强制登出

这种方案有效解决了会话丢失问题，同时保持了所有现有功能的完整性。
